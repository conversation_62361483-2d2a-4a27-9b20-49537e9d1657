package org.hassan.task.dto;

import org.hassan.task.model.OrderSide;
import java.math.BigDecimal;

public class OrderRequest {
    
    private String portfolioId;
    private String isin;
    private OrderSide side;
    private BigDecimal quantity;
    
    // Default constructor
    public OrderRequest() {}
    
    // Constructor
    public OrderRequest(String portfolioId, String isin, OrderSide side, BigDecimal quantity) {
        this.portfolioId = portfolioId;
        this.isin = isin;
        this.side = side;
        this.quantity = quantity;
    }
    
    // Getters and Setters
    public String getPortfolioId() {
        return portfolioId;
    }
    
    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }
    
    public String getIsin() {
        return isin;
    }
    
    public void setIsin(String isin) {
        this.isin = isin;
    }
    
    public OrderSide getSide() {
        return side;
    }
    
    public void setSide(OrderSide side) {
        this.side = side;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
}
