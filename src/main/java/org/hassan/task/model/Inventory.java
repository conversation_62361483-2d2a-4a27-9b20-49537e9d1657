package org.hassan.task.model;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "inventory")
public class Inventory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "portfolio_id", nullable = false)
    private String portfolioId;
    
    @Column(name = "isin", nullable = false)
    private String isin;
    
    @Column(name = "quantity", nullable = false, precision = 19, scale = 2)
    private BigDecimal quantity;
    
    // Default constructor
    public Inventory() {}
    
    // Constructor
    public Inventory(String portfolioId, String isin, BigDecimal quantity) {
        this.portfolioId = portfolioId;
        this.isin = isin;
        this.quantity = quantity;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getPortfolioId() {
        return portfolioId;
    }
    
    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }
    
    public String getIsin() {
        return isin;
    }
    
    public void setIsin(String isin) {
        this.isin = isin;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
}
