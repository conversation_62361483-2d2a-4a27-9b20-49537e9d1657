package org.hassan.task.model;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "buying_power")
public class BuyingPower {
    
    @Id
    @Column(name = "portfolio_id")
    private String portfolioId;
    
    @Column(name = "amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal amount;
    
    // Default constructor
    public BuyingPower() {}
    
    // Constructor
    public BuyingPower(String portfolioId, BigDecimal amount) {
        this.portfolioId = portfolioId;
        this.amount = amount;
    }
    
    // Getters and Setters
    public String getPortfolioId() {
        return portfolioId;
    }
    
    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
