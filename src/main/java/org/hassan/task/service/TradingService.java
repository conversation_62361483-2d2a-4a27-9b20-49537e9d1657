package org.hassan.task.service;

import org.hassan.task.dto.OrderRequest;
import org.hassan.task.dto.OrderResponse;
import org.hassan.task.exception.InsufficientBuyingPowerException;
import org.hassan.task.exception.InsufficientInventoryException;
import org.hassan.task.exception.OrderCannotBeCancelledException;
import org.hassan.task.exception.OrderNotFoundException;
import org.hassan.task.model.Order;
import org.hassan.task.model.OrderSide;
import org.hassan.task.model.OrderStatus;
import org.hassan.task.model.BuyingPower;
import org.hassan.task.repository.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.Optional;

@Service
public class TradingService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private MarketDataService marketDataService;
    
    @Autowired
    private BuyingPowerService buyingPowerService;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Transactional
    public OrderResponse createOrder(OrderRequest orderRequest) {
        BigDecimal marketPrice = marketDataService.getPrice(orderRequest.getIsin());

        if (orderRequest.getSide() == OrderSide.BUY) {
            return createBuyOrder(orderRequest, marketPrice);
        } else {
            return createSellOrder(orderRequest, marketPrice);
        }
    }

    private OrderResponse createBuyOrder(OrderRequest orderRequest, BigDecimal marketPrice) {
        BigDecimal totalCost = orderRequest.getQuantity().multiply(marketPrice);

        if (!buyingPowerService.hasSufficientBuyingPower(orderRequest.getPortfolioId(), totalCost)) {
            throw new InsufficientBuyingPowerException("Insufficient buying power");
        }

        Order order = new Order(
            orderRequest.getPortfolioId(),
            orderRequest.getIsin(),
            orderRequest.getSide(),
            orderRequest.getQuantity(),
            marketPrice,
            OrderStatus.CREATED
        );
        order = orderRepository.save(order);

        BuyingPower buyingPower = buyingPowerService.getBuyingPower(orderRequest.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().subtract(totalCost);
        buyingPowerService.updateBuyingPower(orderRequest.getPortfolioId(), newBuyingPower);

        inventoryService.addToInventory(orderRequest.getPortfolioId(), orderRequest.getIsin(), orderRequest.getQuantity());

        return convertToOrderResponse(order);
    }

    private OrderResponse createSellOrder(OrderRequest orderRequest, BigDecimal marketPrice) {
        if (!inventoryService.hasSufficientShares(orderRequest.getPortfolioId(), orderRequest.getIsin(), orderRequest.getQuantity())) {
            throw new InsufficientInventoryException("Insufficient inventory");
        }

        Order order = new Order(
            orderRequest.getPortfolioId(),
            orderRequest.getIsin(),
            orderRequest.getSide(),
            orderRequest.getQuantity(),
            marketPrice,
            OrderStatus.CREATED
        );
        order = orderRepository.save(order);

        inventoryService.removeFromInventory(orderRequest.getPortfolioId(), orderRequest.getIsin(), orderRequest.getQuantity());

        BigDecimal proceeds = orderRequest.getQuantity().multiply(marketPrice);
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(orderRequest.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().add(proceeds);
        buyingPowerService.updateBuyingPower(orderRequest.getPortfolioId(), newBuyingPower);

        return convertToOrderResponse(order);
    }

    public Optional<OrderResponse> getOrder(Long orderId) {
        return orderRepository.findById(orderId)
                .map(this::convertToOrderResponse);
    }

    @Transactional
    public OrderResponse cancelOrder(Long orderId) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new OrderNotFoundException("Order not found"));

        if (order.getStatus() != OrderStatus.CREATED) {
            throw new OrderCannotBeCancelledException("Order cannot be cancelled");
        }

        if (order.getSide() == OrderSide.BUY) {
            reverseBuyOrder(order);
        } else {
            reverseSellOrder(order);
        }

        order.setStatus(OrderStatus.CANCELLED);
        order = orderRepository.save(order);

        return convertToOrderResponse(order);
    }

    private void reverseBuyOrder(Order order) {
        BigDecimal refund = order.getQuantity().multiply(order.getPrice());
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(order.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().add(refund);
        buyingPowerService.updateBuyingPower(order.getPortfolioId(), newBuyingPower);

        inventoryService.removeFromInventory(order.getPortfolioId(), order.getIsin(), order.getQuantity());
    }

    private void reverseSellOrder(Order order) {
        BigDecimal deduction = order.getQuantity().multiply(order.getPrice());
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(order.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().subtract(deduction);
        buyingPowerService.updateBuyingPower(order.getPortfolioId(), newBuyingPower);

        inventoryService.addToInventory(order.getPortfolioId(), order.getIsin(), order.getQuantity());
    }

    private OrderResponse convertToOrderResponse(Order order) {
        return new OrderResponse(
            order.getId(),
            order.getPortfolioId(),
            order.getIsin(),
            order.getSide(),
            order.getQuantity(),
            order.getPrice(),
            order.getStatus()
        );
    }
}
