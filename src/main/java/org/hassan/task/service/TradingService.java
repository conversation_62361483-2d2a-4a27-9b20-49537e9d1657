package org.hassan.task.service;

import org.hassan.task.dto.OrderRequest;
import org.hassan.task.dto.OrderResponse;
import org.hassan.task.model.Order;
import org.hassan.task.model.OrderSide;
import org.hassan.task.model.OrderStatus;
import org.hassan.task.model.BuyingPower;
import org.hassan.task.repository.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.Optional;

@Service
public class TradingService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private MarketDataService marketDataService;
    
    @Autowired
    private BuyingPowerService buyingPowerService;
    
    @Autowired
    private InventoryService inventoryService;
    
    /**
     * Creates a new order (buy or sell).
     */
    @Transactional
    public OrderResponse createOrder(OrderRequest orderRequest) {
        BigDecimal marketPrice = marketDataService.getPrice(orderRequest.getIsin());
        
        if (orderRequest.getSide() == OrderSide.BUY) {
            return createBuyOrder(orderRequest, marketPrice);
        } else {
            return createSellOrder(orderRequest, marketPrice);
        }
    }
    
    /**
     * Creates a buy order.
     */
    private OrderResponse createBuyOrder(OrderRequest orderRequest, BigDecimal marketPrice) {
        BigDecimal totalCost = orderRequest.getQuantity().multiply(marketPrice);
        
        // Check if portfolio has sufficient buying power
        if (!buyingPowerService.hasSufficientBuyingPower(orderRequest.getPortfolioId(), totalCost)) {
            throw new IllegalArgumentException("Insufficient buying power");
        }
        
        // Create and save the order
        Order order = new Order(
            orderRequest.getPortfolioId(),
            orderRequest.getIsin(),
            orderRequest.getSide(),
            orderRequest.getQuantity(),
            marketPrice,
            OrderStatus.CREATED
        );
        order = orderRepository.save(order);
        
        // Update buying power
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(orderRequest.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().subtract(totalCost);
        buyingPowerService.updateBuyingPower(orderRequest.getPortfolioId(), newBuyingPower);
        
        // Update inventory
        inventoryService.addToInventory(orderRequest.getPortfolioId(), orderRequest.getIsin(), orderRequest.getQuantity());
        
        return convertToOrderResponse(order);
    }
    
    /**
     * Creates a sell order.
     */
    private OrderResponse createSellOrder(OrderRequest orderRequest, BigDecimal marketPrice) {
        // Check if portfolio has sufficient shares
        if (!inventoryService.hasSufficientShares(orderRequest.getPortfolioId(), orderRequest.getIsin(), orderRequest.getQuantity())) {
            throw new IllegalArgumentException("Insufficient inventory");
        }
        
        // Create and save the order
        Order order = new Order(
            orderRequest.getPortfolioId(),
            orderRequest.getIsin(),
            orderRequest.getSide(),
            orderRequest.getQuantity(),
            marketPrice,
            OrderStatus.CREATED
        );
        order = orderRepository.save(order);
        
        // Update inventory
        inventoryService.removeFromInventory(orderRequest.getPortfolioId(), orderRequest.getIsin(), orderRequest.getQuantity());
        
        // Update buying power
        BigDecimal proceeds = orderRequest.getQuantity().multiply(marketPrice);
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(orderRequest.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().add(proceeds);
        buyingPowerService.updateBuyingPower(orderRequest.getPortfolioId(), newBuyingPower);
        
        return convertToOrderResponse(order);
    }
    
    /**
     * Retrieves an order by ID.
     */
    public Optional<OrderResponse> getOrder(Long orderId) {
        return orderRepository.findById(orderId)
                .map(this::convertToOrderResponse);
    }
    
    /**
     * Cancels an order.
     */
    @Transactional
    public OrderResponse cancelOrder(Long orderId) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new IllegalArgumentException("Order not found"));
        
        if (order.getStatus() != OrderStatus.CREATED) {
            throw new IllegalArgumentException("Order cannot be cancelled");
        }
        
        // Reverse the order effects
        if (order.getSide() == OrderSide.BUY) {
            reverseBuyOrder(order);
        } else {
            reverseSellOrder(order);
        }
        
        // Update order status
        order.setStatus(OrderStatus.CANCELLED);
        order = orderRepository.save(order);
        
        return convertToOrderResponse(order);
    }
    
    /**
     * Reverses the effects of a buy order.
     */
    private void reverseBuyOrder(Order order) {
        // Return buying power
        BigDecimal refund = order.getQuantity().multiply(order.getPrice());
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(order.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().add(refund);
        buyingPowerService.updateBuyingPower(order.getPortfolioId(), newBuyingPower);
        
        // Remove from inventory
        inventoryService.removeFromInventory(order.getPortfolioId(), order.getIsin(), order.getQuantity());
    }
    
    /**
     * Reverses the effects of a sell order.
     */
    private void reverseSellOrder(Order order) {
        // Deduct buying power
        BigDecimal deduction = order.getQuantity().multiply(order.getPrice());
        BuyingPower buyingPower = buyingPowerService.getBuyingPower(order.getPortfolioId());
        BigDecimal newBuyingPower = buyingPower.getAmount().subtract(deduction);
        buyingPowerService.updateBuyingPower(order.getPortfolioId(), newBuyingPower);
        
        // Add back to inventory
        inventoryService.addToInventory(order.getPortfolioId(), order.getIsin(), order.getQuantity());
    }
    
    /**
     * Converts Order entity to OrderResponse DTO.
     */
    private OrderResponse convertToOrderResponse(Order order) {
        return new OrderResponse(
            order.getId(),
            order.getPortfolioId(),
            order.getIsin(),
            order.getSide(),
            order.getQuantity(),
            order.getPrice(),
            order.getStatus()
        );
    }
}
