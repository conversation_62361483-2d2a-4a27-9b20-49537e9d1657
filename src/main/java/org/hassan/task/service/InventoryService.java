package org.hassan.task.service;

import org.hassan.task.model.Inventory;
import org.hassan.task.repository.InventoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Optional;

@Service
public class InventoryService {
    
    @Autowired
    private InventoryRepository inventoryRepository;
    
    /**
     * Gets the inventory for a specific portfolio and ISIN.
     */
    public Optional<Inventory> getInventory(String portfolioId, String isin) {
        return inventoryRepository.findByPortfolioIdAndIsin(portfolioId, isin);
    }
    
    /**
     * Checks if a portfolio has sufficient shares to sell.
     */
    public boolean hasSufficientShares(String portfolioId, String isin, BigDecimal requiredQuantity) {
        Optional<Inventory> inventory = getInventory(portfolioId, isin);
        return inventory.isPresent() && 
               inventory.get().getQuantity().compareTo(requiredQuantity) >= 0;
    }
    
    /**
     * Updates inventory after a buy order.
     */
    public Inventory addToInventory(String portfolioId, String isin, BigDecimal quantity) {
        Optional<Inventory> existingInventory = getInventory(portfolioId, isin);
        
        if (existingInventory.isPresent()) {
            Inventory inventory = existingInventory.get();
            BigDecimal newQuantity = inventory.getQuantity().add(quantity);
            inventory.setQuantity(newQuantity);
            return inventoryRepository.save(inventory);
        } else {
            Inventory newInventory = new Inventory(portfolioId, isin, quantity);
            return inventoryRepository.save(newInventory);
        }
    }
    
    /**
     * Updates inventory after a sell order.
     */
    public Inventory removeFromInventory(String portfolioId, String isin, BigDecimal quantity) {
        Inventory inventory = getInventory(portfolioId, isin)
                .orElseThrow(() -> new IllegalStateException("Inventory not found"));
        
        BigDecimal newQuantity = inventory.getQuantity().subtract(quantity);
        
        if (newQuantity.compareTo(BigDecimal.ZERO) == 0) {
            // Remove inventory entry if quantity becomes zero
            inventoryRepository.delete(inventory);
            return null;
        } else {
            inventory.setQuantity(newQuantity);
            return inventoryRepository.save(inventory);
        }
    }
}
