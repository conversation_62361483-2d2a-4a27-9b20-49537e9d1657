package org.hassan.task.service;

import org.hassan.task.model.BuyingPower;
import org.hassan.task.repository.BuyingPowerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;

@Service
public class BuyingPowerService {
    
    private static final BigDecimal INITIAL_BUYING_POWER = new BigDecimal("5000.00");
    
    @Autowired
    private BuyingPowerRepository buyingPowerRepository;
    
    /**
     * Gets the buying power for a portfolio. If it doesn't exist, creates it with initial amount.
     */
    public BuyingPower getBuyingPower(String portfolioId) {
        return buyingPowerRepository.findById(portfolioId)
                .orElseGet(() -> createInitialBuyingPower(portfolioId));
    }
    
    /**
     * Creates initial buying power for a new portfolio.
     */
    private BuyingPower createInitialBuyingPower(String portfolioId) {
        BuyingPower buyingPower = new BuyingPower(portfolioId, INITIAL_BUYING_POWER);
        return buyingPowerRepository.save(buyingPower);
    }
    
    /**
     * Updates the buying power for a portfolio.
     */
    public BuyingPower updateBuyingPower(String portfolioId, BigDecimal newAmount) {
        BuyingPower buyingPower = getBuyingPower(portfolioId);
        buyingPower.setAmount(newAmount);
        return buyingPowerRepository.save(buyingPower);
    }
    
    /**
     * Checks if a portfolio has sufficient buying power for a purchase.
     */
    public boolean hasSufficientBuyingPower(String portfolioId, BigDecimal requiredAmount) {
        BuyingPower buyingPower = getBuyingPower(portfolioId);
        return buyingPower.getAmount().compareTo(requiredAmount) >= 0;
    }
}
