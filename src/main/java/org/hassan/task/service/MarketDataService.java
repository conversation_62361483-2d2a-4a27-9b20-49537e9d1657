package org.hassan.task.service;

import org.springframework.stereotype.Service;
import java.math.BigDecimal;

@Service
public class MarketDataService {
    
    /**
     * Retrieves the market price for a given security (ISIN).
     * For simplicity, this returns a fixed price of 100.00 for all securities.
     * In a real implementation, this would connect to a market data provider.
     */
    public BigDecimal getPrice(String isin) {
        // For this implementation, return a fixed price for all securities
        return new BigDecimal("100.00");
    }
}
