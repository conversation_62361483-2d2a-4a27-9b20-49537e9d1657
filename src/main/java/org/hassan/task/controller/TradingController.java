package org.hassan.task.controller;

import org.hassan.task.dto.ErrorResponse;
import org.hassan.task.dto.OrderRequest;
import org.hassan.task.dto.OrderResponse;
import org.hassan.task.service.TradingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/orders")
public class TradingController {
    
    @Autowired
    private TradingService tradingService;
    
    /**
     * Creates a new order (buy or sell).
     */
    @PostMapping
    public ResponseEntity<?> createOrder(@RequestBody OrderRequest orderRequest) {
        try {
            OrderResponse orderResponse = tradingService.createOrder(orderRequest);
            return ResponseEntity.ok(orderResponse);
        } catch (IllegalArgumentException e) {
            ErrorResponse errorResponse = new ErrorResponse(400, e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(500, "Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Retrieves an order by ID.
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getOrder(@PathVariable Long id) {
        try {
            Optional<OrderResponse> orderResponse = tradingService.getOrder(id);
            if (orderResponse.isPresent()) {
                return ResponseEntity.ok(orderResponse.get());
            } else {
                ErrorResponse errorResponse = new ErrorResponse(404, "Order not found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(500, "Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Cancels an order.
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> cancelOrder(@PathVariable Long id) {
        try {
            OrderResponse orderResponse = tradingService.cancelOrder(id);
            return ResponseEntity.ok(orderResponse);
        } catch (IllegalArgumentException e) {
            if (e.getMessage().equals("Order not found")) {
                ErrorResponse errorResponse = new ErrorResponse(404, e.getMessage());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            } else {
                ErrorResponse errorResponse = new ErrorResponse(400, e.getMessage());
                return ResponseEntity.badRequest().body(errorResponse);
            }
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(500, "Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
